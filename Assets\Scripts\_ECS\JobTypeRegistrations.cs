using Unity.Entities;
using Unity.Jobs;
using GimmeDOTSGeometry;
using PlayerFAP.Components.SpatialPartitioning;
using Position3D = GimmeDOTSGeometry.Position3D;

/// <summary>
/// Centralized job type registrations for Burst compilation support
/// Following rules.md: All generic job types must be registered for Burst compilation
/// </summary>

// Legacy Position3D KDTree job registrations
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.KDTreeConstructionJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.GetPointsInRadiusJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.GetPointsInBoundsJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.GetPointsInRadiiJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.GetPointsInMultipleBoundsJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<Position3D>.GetNearestNeighborJob))]

// Enhanced EnemyPositionData KDTree job registrations for hybrid approach
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.KDTreeConstructionJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.GetPointsInRadiusJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.GetPointsInBoundsJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.GetPointsInRadiiJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.GetPointsInMultipleBoundsJob))]
[assembly: Unity.Jobs.RegisterGenericJobType(typeof(Native3DKDTree<EnemyPositionData>.GetNearestNeighborJob))]

namespace PlayerFAP.JobRegistrations
{
    /// <summary>
    /// This class exists solely to ensure job type registrations are processed
    /// The actual registrations are done via assembly attributes above
    /// </summary>
    public static class KDTreeJobRegistrations
    {
        // This method can be called to ensure the assembly is loaded and registrations are processed
        public static void EnsureRegistrations()
        {
            // Empty method - registrations happen via assembly attributes
        }
    }
}
