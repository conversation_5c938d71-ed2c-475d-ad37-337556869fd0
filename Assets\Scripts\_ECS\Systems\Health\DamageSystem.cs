using Unity.Entities;
using PlayerFAP.Components;
using PlayerFAP.Systems.UI;
using GPUAnimationCrowds;
using Unity.Jobs;
using UnityEngine;
using PlayerFAP.Managers;
using PlayerFAP.ScriptableObjects;

namespace PlayerFAP.Systems.Health
{
// Define a custom ECB system to handle commands after DamageSystem and before HealthBarSpawnSystem
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(DamageSystem))]
    [UpdateBefore(typeof(HealthBarSpawnSystem))]
    public partial class DamageECBSystem : EntityCommandBufferSystem {}

    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(HealthBarSpawnSystem))]
    
    public partial class DamageSystem : SystemBase
    {
        private EntityCommandBuffer.ParallelWriter ecb;
        private DamageECBSystem commandBufferSystem;

        protected override void OnCreate()
        {
            // Use the custom ECB system
            commandBufferSystem = World.GetOrCreateSystemManaged<DamageECBSystem>();
        }

        // Can't use BurstCompile because we're using DebugLogManager.Instance.Log
        private partial struct DamageJob : IJobEntity
        {
            public EntityCommandBuffer.ParallelWriter ECB;
            public float DeathDissolveStartTime;
            public float DeathDissolveDuration;
            [Unity.Collections.ReadOnly] public ComponentLookup<DeadTag> DeadTagLookup;

            public void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                in DamageEvent damageEvent, ref HealthComponent health)
            {
                health.CurrentHealth -= damageEvent.DamageAmount;
                ECB.RemoveComponent<DamageEvent>(sortKey, entity);

                if (health.CurrentHealth <= 0)
                {
                    health.CurrentHealth = 0;

                    // Add DeadTag to mark entity as dead (if not already added)
                    if (!DeadTagLookup.HasComponent(entity))
                    {
                        // Add DeadTag to mark entity as dead
                        ECB.AddComponent<DeadTag>(sortKey, entity);

                        // Add UndetectableTag to prevent detection after death
                        ECB.AddComponent<UndetectableTag>(sortKey, entity);

                        // Remove DetectedTag if it exists - IMMEDIATE removal for dead entities
                        if (SystemAPI.HasComponent<DetectedTag>(entity))
                        {
                            ECB.RemoveComponent<DetectedTag>(sortKey, entity);
                        }

                        // IMMEDIATE health bar cleanup for dead entities - no grace period
                        if (SystemAPI.HasComponent<HealthBarUIReference>(entity))
                        {
                            var uiRef = SystemAPI.GetComponent<HealthBarUIReference>(entity);
                            if (uiRef.UIEntity != Entity.Null)
                            {
                                // Add HideHealthBarTag for immediate cleanup
                                ECB.AddComponent<HideHealthBarTag>(sortKey, uiRef.UIEntity);

                                // Remove the UI reference from the dead entity
                                ECB.RemoveComponent<HealthBarUIReference>(sortKey, entity);
                            }
                        }

                        // Remove AlwaysShowHealthBarTag if it exists
                        if (SystemAPI.HasComponent<AlwaysShowHealthBarTag>(entity))
                        {
                            ECB.RemoveComponent<AlwaysShowHealthBarTag>(sortKey, entity);
                        }

                        // Remove any cleanup timers since we're handling death immediately
                        if (SystemAPI.HasComponent<HealthBarCleanupTimerComponent>(entity))
                        {
                            ECB.RemoveComponent<HealthBarCleanupTimerComponent>(sortKey, entity);
                        }

                        // Add DeathTimerComponent to track time for dissolve effect
                        ECB.AddComponent(sortKey, entity, new DeathTimerComponent
                        {
                            ElapsedTime = 0f,
                            DissolveStartTime = DeathDissolveStartTime,
                            DissolveDuration = DeathDissolveDuration,
                            DissolveStarted = false
                        });

                        // Add DissolveEffectComponent for controlling the shader
                        ECB.AddComponent(sortKey, entity, new DissolveEffectComponent
                        {
                            DissolveAmount = 0f
                        });

                        DebugLogManager.Instance.Log($"Entity {entity.Index} died. Adding DeadTag and DeathTimerComponent.");
                    }
                }
                else
                {
                    var hitTag = new HitTag
                    {
                        HitTime = 0f,
                        HitAnimationDuration = .5f // Adjust as needed
                    };
                    ECB.AddComponent<HitTag>(sortKey, entity, hitTag);

                    DebugLogManager.Instance.Log(
                        $"[DamageSystem] Added HitTag to entity {entity.Index} with duration {hitTag.HitAnimationDuration}",
                        DebugLogSettings.LogType.EnemyAnimation);
                }
            }
        }

        protected override void OnUpdate()
        {
            ecb = commandBufferSystem.CreateCommandBuffer().AsParallelWriter();

            // Get configuration values
            var config = HealthSystemManager.Instance.Configuration;

            var job = new DamageJob {
                ECB = ecb,
                DeathDissolveStartTime = config.deathDissolveStartTime,
                DeathDissolveDuration = config.deathDissolveDuration,
                DeadTagLookup = SystemAPI.GetComponentLookup<DeadTag>(true) // Read-only lookup
            };

            // Schedule the job with dependency chaining
            JobHandle jobHandle = job.ScheduleParallel(Dependency);
            commandBufferSystem.AddJobHandleForProducer(jobHandle);
            Dependency = jobHandle;
        }
    }
}
