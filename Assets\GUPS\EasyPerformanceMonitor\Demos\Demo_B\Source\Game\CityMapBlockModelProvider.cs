// Unity
using UnityEngine;

namespace GUPS.EasyPerformanceMonitor.Demos.B
{
    /// <summary>
    /// Returns the BlockModel for the City Map.
    /// </summary>
    public class CityMapBlockModelProvider : AHeightArrayBlockModelProvider
    {
        /// <summary>
        /// Returns the height map of the City Map.
        /// </summary>
        protected override int[,] MapHeight {
            get
            {
                return new int[,] {
                    { 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14, 13, 1, 1, 1, 1, 8, 8, 8, 1, 1 },
                    { 1, 1, 15, 15, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14, 13, 1, 1, 1, 1, 8, 8, 8, 1, 1, 1 },
                    { 1, 12, 15, 15, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1 },
                    { 1, 1, 15, 15, 15, 1, 14, 14, 14, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 1, 1, 1, 1, 1, 8, 8, 8, 8, 8, 1 },
                    { 1, 1, 10, 10, 10, 1, 14, 14, 14, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 1, 1, 1, 1, 1, 6, 6, 6, 6, 1, 1 },
                    { 1, 1, 10, 10, 10, 1, 14, 14, 14, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 10, 1, 1, 1, 1, 6, 6, 6, 6, 1, 1 },
                    { 1, 1, 10, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 10, 10, 10, 10, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 10, 10, 10, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 10, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 13, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17, 18, 17, 1, 1, 1, 1, 1, 14, 14, 14 },
                    { 1, 1, 15, 15, 15, 1, 1, 1, 14, 14, 14, 1, 1, 1, 1, 1, 1, 17, 18, 17, 1, 1, 1, 1, 1, 1, 12, 14, 14, 14 },
                    { 1, 1, 15, 15, 15, 1, 1, 14, 14, 14, 13, 1, 1, 1, 1, 1, 1, 17, 18, 17, 1, 1, 1, 1, 1, 1, 1, 14, 14, 14 },
                    { 1, 1, 15, 15, 15, 1, 1, 14, 14, 14, 12, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12 },
                    { 1, 10, 17, 17, 17, 1, 1, 2, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 10, 10, 10, 10 },
                    { 1, 10, 17, 17, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1, 10, 10, 10 },
                    { 1, 10, 17, 17, 17, 1, 1, 1, 16, 17, 16, 1, 1, 1, 1, 1, 1, 1, 6, 6, 1, 1, 1, 1, 1, 1, 1, 10, 10, 10 },
                    { 1, 1, 15, 15, 15, 1, 1, 1, 16, 16, 16, 1, 1, 1, 1, 1, 1, 1, 5, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 15, 15, 15, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 2, 1, 1, 1, 1, 1, 1, 1, 1, 3, 7, 5, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 2, 1, 1, 1, 1, 13, 15, 15, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 12, 12, 12, 1, 1, 1, 15, 15, 15, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 10, 1, 1, 1, 1, 1, 1, 12, 12 },
                    { 1, 1, 12, 12, 12, 1, 1, 1, 15, 16, 15, 10, 1, 1, 1, 1, 1, 1, 1, 10, 10, 1, 1, 1, 1, 1, 1, 1, 12, 12 },
                    { 1, 4, 12, 12, 12, 12, 1, 1, 1, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 3, 12, 12, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 18, 18, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 8 },
                    { 1, 1, 1, 18, 19, 18, 1, 1, 1, 1, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 6, 15, 15 },
                    { 1, 1, 1, 18, 18, 18, 1, 1, 8, 12, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 5, 15, 15 },
                    { 1, 1, 1, 1, 2, 3, 1, 1, 1, 12, 12, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 7, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 1 },
                    { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 8, 1, 1, 1, 1, 1, 1 },
                };
            }
        }

        /// <summary>
        /// Returns a grey color.
        /// </summary>
        public override Color Color
        {
            get
            {
                return new Color(0.5f, 0.5f, 0.5f);
            }
        }
    }
}