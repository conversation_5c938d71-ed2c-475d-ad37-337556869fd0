using GPUAnimationCrowds;
using GPUECSAnimationBaker.Engine.AnimatorSystem;
using PlayerFAP.Authorings;
using PlayerFAP.Components;
using PlayerFAP.Mono.UI.Health;
using PlayerFAP.Systems.Health;
using PlayerFAP.Systems.UI;
using PlayerFAP.Tags;
using ProjectDawn.Navigation;
using Systems.PlayerFAP;
using Unity.Entities;
using Unity.Physics;
using UnityEngine;
#if RUKHANKA_ANIMATION
using Rukhanka;
#endif

namespace PlayerFAP.Systems
{
    // This system handles death-related tasks for enemies (not animation - that's handled by EnemyAnimationSystem)
    [UpdateAfter(typeof(DamageSystem))]
    [UpdateBefore(typeof(EnemyAnimationSystem))]
    public partial class DeathAnimationSystem : SystemBase
    {
        private EntityCommandBufferSystem m_EndSimulationEcbSystem;

#if RUKHANKA_ANIMATION
        // For accessing EnemyRukhankaAnimationState directly
        private ComponentLookup<EnemyAnimationStateComponent> m_EnemyRukhankaAnimationStateLookup;
        // For accessing AnimationToProcessComponent buffer
        private BufferLookup<AnimationToProcessComponent> m_AnimationToProcessLookup;
#endif

        protected override void OnCreate()
        {
            m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
            RequireForUpdate<DeadTag>();

#if RUKHANKA_ANIMATION
            // Initialize EnemyRukhankaAnimationState lookup
            m_EnemyRukhankaAnimationStateLookup = GetComponentLookup<EnemyAnimationStateComponent>(false); // false = read-write
            // Initialize AnimationToProcessComponent lookup
            m_AnimationToProcessLookup = GetBufferLookup<AnimationToProcessComponent>(false); // false = read-write
#endif
        }

        protected override void OnUpdate()
        {
            var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();

            bool loggingEnabled = DebugLogManager.Instance.IsLogTypeEnabled(DebugLogSettings.LogType.EnemyAnimation);

#if RUKHANKA_ANIMATION
            // Update EnemyRukhankaAnimationState lookup
            m_EnemyRukhankaAnimationStateLookup.Update(this);
            // Update AnimationToProcessComponent lookup
            m_AnimationToProcessLookup.Update(this);
#endif

            // Handle newly dead entities with GPU ECS Animator
            Entities
                .WithNone<DeathAnimationStartedTag>()
                .WithAll<DeadTag, CharacterAnimatorReference, AnimationSystemTypeComponent>()
                .ForEach((Entity entity,
                         ref CharacterMovementState movementState,
                         in CharacterAnimatorReference animatorRef,
                         in AnimationSystemTypeComponent animSystemType) =>
                {
                    // Only process if using GPU ECS Animator
                    if (animSystemType.Type != (int)AnimationSystemType.GPUECSAnimator)
                        return;

                    if (animatorRef.AnimatorEntity == Entity.Null)
                        return;

                    movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
                    movementState.IsMoving = false;
                    movementState.IsAttacking = false;

                    ecb.AddComponent<DeathAnimationStartedTag>(entity);

                    if (loggingEnabled)
                    {
                        DebugLogManager.Instance.Log($"Entity {entity.Index} marked for death animation (GPU ECS)", DebugLogSettings.LogType.EnemyAnimation);
                    }
                }).WithoutBurst().Run();

#if RUKHANKA_ANIMATION
            // Handle newly dead entities with Rukhanka
            Entities
                .WithNone<DeathAnimationStartedTag>()
                .WithAll<DeadTag, EnemyAnimationStateComponent>()
                .ForEach((Entity entity,
                         ref CharacterMovementState movementState,
                         ref EnemyAnimationStateComponent rukhankaAnimator,
                         in AnimationSystemTypeComponent animSystemType,
                         in DynamicBuffer<RukhankaClipBuffer> clipBuffer) =>
                {
                    if (loggingEnabled)
                    {
                        DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] Checking entity {entity.Index} for death animation. AnimationSystemType: {animSystemType.Type}, Has EnemyRukhankaAnimationState: true", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Skip if not using Rukhanka
                    if (animSystemType.Type != (int)AnimationSystemType.Rukhanka)
                    {
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] Entity {entity.Index} skipped: AnimationSystemType is not Rukhanka (actual: {animSystemType.Type})", DebugLogSettings.LogType.EnemyAnimation);
                        }
                        return;
                    }

                    // Get animation name for logging
                    string animationName = "Dead";
                    bool hasValidClip = false;
                    int deadAnimIndex = (int)EnemyAnimationIDs.Dead;

                    if (deadAnimIndex >= 0 && deadAnimIndex < clipBuffer.Length)
                    {
                        string clipName = clipBuffer[deadAnimIndex].name.ToString();
                        if (!string.IsNullOrEmpty(clipName))
                        {
                            animationName = clipName;
                        }
                        hasValidClip = clipBuffer[deadAnimIndex].clipBlob.IsCreated;
                    }

                    // Update the movement state to indicate this entity is dead
                    movementState.CurrentAnimationID = deadAnimIndex;
                    movementState.IsMoving = false;
                    movementState.IsAttacking = false;

                    // Store current animation ID in Rukhanka component
                    rukhankaAnimator.CurrentAnimationIndex = deadAnimIndex;

                    // Set the Rukhanka animation index and reset time if needed
                    if (m_EnemyRukhankaAnimationStateLookup.HasComponent(entity))
                    {
                        var rukhankaComp = m_EnemyRukhankaAnimationStateLookup.GetRefRW(entity);

                        // Always force the death animation regardless of current state
                        rukhankaComp.ValueRW.CurrentAnimationIndex = deadAnimIndex;
                        rukhankaComp.ValueRW.NormalizedTime = 0f;

                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] Entity {entity.Index} death animation state set: " +
                                $"Index={deadAnimIndex}, Time=0.0", DebugLogSettings.LogType.EnemyAnimation);
                        }

                        // We don't play the animation directly here anymore
                        // The RukhankaAnimationProcessSystem will handle playing the animation
                        if (loggingEnabled)
                        {
                            if (hasValidClip)
                            {
                                DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] Set death animation for entity {entity.Index}: '{animationName}'",
                                    DebugLogSettings.LogType.EnemyAnimation);
                            }
                            else
                            {
                                DebugLogManager.Instance.LogWarning($"[DeathAnimationSystem][Rukhanka] Invalid death animation clip for entity {entity.Index}");
                            }
                        }
                    }

                    // Add tag to mark that death animation has started
                    ecb.AddComponent<DeathAnimationStartedTag>(entity);

                    if (loggingEnabled)
                    {
                        string validityMsg = hasValidClip ? "valid clip" : "NO VALID CLIP";
                        DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] Entity {entity.Index} marked for death animation. Playing '{animationName}' (ID: {deadAnimIndex}, {validityMsg})", DebugLogSettings.LogType.EnemyAnimation);
                        DebugLogManager.Instance.Log($"[DeathAnimationSystem][Rukhanka] movementState: CurrentAnimationID={movementState.CurrentAnimationID}, IsMoving={movementState.IsMoving}, IsAttacking={movementState.IsAttacking}", DebugLogSettings.LogType.EnemyAnimation);
                    }
                }).WithoutBurst().Run();
#endif

            // Process common death-related tasks for all newly dead entities
            Entities
                .WithNone<DeathProcessedTag>()
                .WithAll<DeadTag, DeathAnimationStartedTag>()
                .ForEach((Entity entity) =>
                {

                    // Hide health bar if it exists - IMMEDIATE cleanup for dead entities
                    if (SystemAPI.HasComponent<HealthBarLink>(entity))
                    {
                        var healthBarLink = SystemAPI.GetComponent<HealthBarLink>(entity);
                        if (healthBarLink.HealthBarEntity != Entity.Null)
                        {
                            // Add a tag to hide the health bar
                            ecb.AddComponent<HideHealthBarTag>(healthBarLink.HealthBarEntity);
                            if (loggingEnabled)
                            {
                                DebugLogManager.Instance.Log($"Entity {entity.Index} health bar hidden via HideHealthBarTag", DebugLogSettings.LogType.EnemyAnimation);
                            }
                        }
                    }

                    // IMMEDIATE health bar cleanup - remove UI reference directly
                    if (SystemAPI.HasComponent<HealthBarUIReference>(entity))
                    {
                        var uiRef = SystemAPI.GetComponent<HealthBarUIReference>(entity);
                        if (uiRef.UIEntity != Entity.Null && EntityManager.Exists(uiRef.UIEntity))
                        {
                            if (EntityManager.HasComponent<CompanionLink>(uiRef.UIEntity))
                            {
                                var companionLink = EntityManager.GetComponentData<CompanionLink>(uiRef.UIEntity);
                                var uiObject = companionLink.Companion.Value;
                                if (uiObject != null)
                                {
                                    HealthBarUIManager.Instance?.ReturnUIToPool(uiObject);
                                    if (loggingEnabled)
                                    {
                                        DebugLogManager.Instance.Log($"Entity {entity.Index} health bar UI returned to pool immediately", DebugLogSettings.LogType.EnemyAnimation);
                                    }
                                }
                            }
                        }
                        ecb.RemoveComponent<HealthBarUIReference>(entity);
                        ecb.RemoveComponent<HealthBarLink>(entity);
                    }

                    // Disable physics collider if it exists
                    if (SystemAPI.HasComponent<PhysicsCollider>(entity))
                    {
                        // We can't directly disable a collider, but we can remove it
                        ecb.RemoveComponent<PhysicsCollider>(entity);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} collider disabled", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Remove DetectedTag if it exists to prevent the player from targeting this enemy
                    if (SystemAPI.HasComponent<DetectedTag>(entity))
                    {
                        ecb.RemoveComponent<DetectedTag>(entity);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} DetectedTag removed", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Remove InFOVTag if it exists
                    if (SystemAPI.HasComponent<InFOVTag>(entity))
                    {
                        ecb.RemoveComponent<InFOVTag>(entity);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} InFOVTag removed", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Remove CurrentTargetTag if it exists
                    if (SystemAPI.HasComponent<CurrentTargetTag>(entity))
                    {
                        ecb.RemoveComponent<CurrentTargetTag>(entity);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} CurrentTargetTag removed", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Add a component to make the entity undetectable
                    ecb.AddComponent<UndetectableTag>(entity);
                    if (loggingEnabled)
                    {
                        DebugLogManager.Instance.Log($"Entity {entity.Index} marked as undetectable", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Stop the agent from moving
                    if (SystemAPI.HasComponent<AgentBody>(entity))
                    {
                        var agentBody = SystemAPI.GetComponent<AgentBody>(entity);
                        agentBody.IsStopped = true;
                        SystemAPI.SetComponent(entity, agentBody);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} agent stopped from moving", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Remove SetDestination component to prevent the agent from getting new destinations
                    if (SystemAPI.HasComponent<SetDestination>(entity))
                    {
                        ecb.RemoveComponent<SetDestination>(entity);
                        if (loggingEnabled)
                        {
                            DebugLogManager.Instance.Log($"Entity {entity.Index} SetDestination component removed", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Mark that death has been processed
                    ecb.AddComponent<DeathProcessedTag>(entity);

                    if (loggingEnabled)
                    {
                        DebugLogManager.Instance.Log($"Entity {entity.Index} prepared for death animation", DebugLogSettings.LogType.EnemyAnimation);
                    }
                }).WithoutBurst().Run();

            m_EndSimulationEcbSystem.AddJobHandleForProducer(Dependency);
        }
    }

    // Tag to mark that death animation has started
    public struct DeathAnimationStartedTag : IComponentData
    {
    }
}
