using System;
using System.Collections.Generic;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Jobs;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Physics.Systems;
using Unity.Transforms;
using UnityEngine;
using GimmeDOTSGeometry;
using PlayerFAP.Components;
using PlayerFAP.Components.Detection;
using PlayerFAP.Components.Player;
using PlayerFAP.Components.Weapon;
using PlayerFAP.Tags;
using PlayerFAP.Components.SpatialPartitioning;
using Position3D = GimmeDOTSGeometry.Position3D;
using Module.Mono.Animancer.RealsticFemale;
using Unity.Physics;

// Job type registrations are handled in JobTypeRegistrations.cs

namespace PlayerFAP.Systems.AimDetection
{
    public struct LostTargetEventTag : IComponentData
    {
    }

    public struct UnAimingTargetEventTag : IComponentData
    {
    }

    public struct DetectedEnemyData
    {
        public Entity Entity;
        public float3 Position;
        public float Distance;
        public float Angle;
        public bool IsInFOV;
        public float Score;
    }

    public partial class OptimizedDetectionSystem : SystemBase
    {
        private double lastSwitchTime;
        private AimingConfiguration aimingConfig;

        // Enhanced stability tracking for non-cluster mode
        private Entity potentialTarget = Entity.Null;
        private double potentialTargetStartTime = 0;
        private float potentialTargetStabilityDuration = 0.5f; // Time a target must be "best" before switching

        // Performance optimization: Throttle detection updates for better performance
        private int frameCounter = 0;
        private const int DETECTION_UPDATE_INTERVAL = 4; // Update every 4 frames (15fps instead of 60fps)
        private const int HEALTH_BAR_UPDATE_INTERVAL = 3; // Update health bars every 3 frames (20fps)

        private EntityQuery playerQuery;
        private EntityQuery weaponQuery;
        private EntityQuery detectedEnemiesQuery;
        private EntityQuery inFOVTagQuery;
        private EntityQuery currentTargetTagQuery;
        private EntityQuery detectionTargetQuery;
        private EntityQuery kdTreeQuery;
        private EntityQuery m_EnemyQuery;

        private EntityCommandBuffer ecb;
        private NativeArray<ArchetypeChunk> detectedEnemies;

        protected override void OnCreate()
        {
            // Load AimingConfiguration
            aimingConfig = UnityEngine.Resources.Load<AimingConfiguration>("AimingConfiguration");
            if (aimingConfig == null)
            {
                UnityEngine.Debug.LogWarning("[OptimizedDetectionSystem] AimingConfiguration not found in Resources folder!");
            }

            playerQuery = GetEntityQuery(new EntityQueryBuilder(Allocator.Temp)
                .WithAll<PlayerTag, SphereDetectSensorComponent, LocalToWorld,
                    PlayerHeadTransformComponent>());

            // Subscribe to event for parameter updates from AimingModule (per rules.md)
            EventManager.Subscribe<Events.OnUpdateDetectionParametersEvent>(OnUpdateDetectionParametersEventHandler);
            EventManager.Subscribe<Events.OnChangeConeAngleEvent>(OnUpdateConeAngleEventHandler);


            weaponQuery = GetEntityQuery(ComponentType.ReadOnly<WeaponBodyPartComponent>());

            detectedEnemiesQuery = GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    ComponentType.ReadOnly<EnemyTag>(),
                    ComponentType.ReadOnly<DetectedTag>(),
                    ComponentType.ReadOnly<LocalToWorld>(),
                    ComponentType.ReadOnly<EnemyBodyPartsPositionComponent>()
                }
            });

            inFOVTagQuery = GetEntityQuery(ComponentType.ReadOnly<InFOVTag>());
            currentTargetTagQuery = GetEntityQuery(ComponentType.ReadOnly<CurrentTargetTag>());
            kdTreeQuery = GetEntityQuery(ComponentType.ReadOnly<KDTreeComponent>());
            m_EnemyQuery = GetEntityQuery(
                ComponentType.ReadOnly<EnemyTag>(),
                ComponentType.ReadOnly<LocalToWorld>(),
                ComponentType.Exclude<UndetectableTag>(), // Exclude undetectable entities
                ComponentType.Exclude<DeadTag>() // Exclude dead entities
            );

            // Create singleton entity for detection target
            var entity = EntityManager.CreateEntity();
            EntityManager.AddComponentData(entity, new DetectionTargetComponent());
            EntityManager.AddBuffer<DetectedEnemyBuffer>(entity);

            detectionTargetQuery = GetEntityQuery(ComponentType.ReadWrite<DetectionTargetComponent>());
        }

        protected override void OnUpdate()
        {
            // Performance optimization: Throttle detection updates to reduce CPU load
            frameCounter++;
            if (frameCounter % DETECTION_UPDATE_INTERVAL != 0)
            {
                return; // Skip this frame to maintain 15fps detection instead of 60fps
            }

            // --- AIM STATE MANAGEMENT ---
            // After all detection logic, we will ensure the player transitions to un-aim when all enemies are lost.
            // If a target is acquired again, we remove the unaim event tag.

            if (playerQuery.IsEmpty || weaponQuery.IsEmpty)
                return;

            var playerEntity = playerQuery.GetSingletonEntity();
            var weaponEntity = weaponQuery.GetSingletonEntity();
            var sensor = SystemAPI.GetComponent<SphereDetectSensorComponent>(playerEntity);
            var playerTransform = SystemAPI.GetComponent<PlayerHeadTransformComponent>(playerEntity);
            // All SphereDetectSensorComponent values are now set by OnUpdateDetectionParametersEventHandler

            var playerPosition = playerTransform.Position;
            var playerForward = playerTransform.Forward;

            // Create command buffer and get enemy data - optimized memory allocation
            var ecb = new EntityCommandBuffer(Allocator.TempJob);
            var detectedEnemies = detectedEnemiesQuery.ToArchetypeChunkArray(Allocator.TempJob);

            var currentTime = SystemAPI.Time.ElapsedTime;
            var canSwitchTarget = (currentTime - lastSwitchTime) >= sensor.CheckInterval;

            // Get detection target singleton
            var detectionTargetEntity = SystemAPI.GetSingletonEntity<DetectionTargetComponent>();
            var detectionTarget = SystemAPI.GetComponent<DetectionTargetComponent>(detectionTargetEntity);

            // Create NativeList for collecting detected enemies
            var detectedEnemiesOutput = new NativeList<DetectedEnemyData>(Allocator.TempJob);

            // HYBRID APPROACH: Use KDTree for spatial filtering + SphereCast for physics validation
            if (sensor.UseKDTree && !kdTreeQuery.IsEmpty)
            {
                var kdTreeEntity = kdTreeQuery.GetSingletonEntity();
                var kdTree = SystemAPI.GetComponent<KDTreeComponent>(kdTreeEntity);

                // Use enhanced hybrid tree if available
                if (kdTree.UseHybridTree && kdTree.HybridTree.IsCreated)
                {
                    // PHASE 1: KDTree spatial filtering (O(log n))
                    var spatialCandidates = new NativeList<EnemyPositionData>(Allocator.TempJob);
                    var _job = kdTree.HybridTree.GetPointsInRadius(playerPosition, sensor.DetectionRange, ref spatialCandidates);
                    _job.Complete();

                    if (spatialCandidates.Length > 0)
                    {
                        // PHASE 2: Physics validation on candidates only
                        var physicsWorld = SystemAPI.GetSingleton<PhysicsWorldSingleton>().PhysicsWorld;
                        var filter = new CollisionFilter
                        {
                            BelongsTo = sensor.BelongsTo,
                            CollidesWith = sensor.CollidesWith
                        };

                        foreach (var candidate in spatialCandidates)
                        {
                            // Skip stale data
                            if ((float)currentTime - candidate.LastUpdateTime > 2.0f) continue;

                            // CRITICAL: Skip dead or undetectable entities in hybrid path
                            if (SystemAPI.HasComponent<DeadTag>(candidate.Entity) ||
                                SystemAPI.HasComponent<UndetectableTag>(candidate.Entity))
                            {
                                continue;
                            }

                            // Physics validation: Check line of sight
                            var rayInput = new RaycastInput
                            {
                                Start = playerPosition,
                                End = candidate.Position,
                                Filter = filter
                            };

                            bool hasLineOfSight = !physicsWorld.CastRay(rayInput, out var hit) ||
                                                 hit.Entity == candidate.Entity;

                            if (hasLineOfSight)
                            {
                                // Get enemy body parts for accurate targeting
                                if (SystemAPI.HasComponent<EnemyBodyPartsPositionComponent>(candidate.Entity))
                                {
                                    var enemyBodyParts = SystemAPI.GetComponent<EnemyBodyPartsPositionComponent>(candidate.Entity);
                                    var chestPosition = new float3(candidate.Position.x, enemyBodyParts.ChestPosition.y, candidate.Position.z);
                                    var dirToEnemy = math.normalize(chestPosition - playerPosition);
                                    var angle = math.degrees(math.acos(math.dot(dirToEnemy, playerForward)));
                                    var distance = math.distance(chestPosition, playerPosition);

                                    // --- AAA Dynamic FOV and Sticky Targeting ---
                                    float effectiveFOV = sensor.DetectionAngle;
                                    bool isSticky = false;

                                    if (detectionTarget.CurrentTarget == candidate.Entity)
                                    {
                                        if (angle <= sensor.DetectionAngle * 0.5f + sensor.stickyAngle &&
                                            distance <= sensor.DetectionRange + sensor.stickyDistance)
                                        {
                                            effectiveFOV += sensor.dynamicFOVExpansion;
                                            isSticky = true;
                                        }
                                    }
                                    else if (angle > sensor.DetectionAngle * 0.5f - sensor.dynamicFOVExpansion)
                                    {
                                        effectiveFOV += sensor.dynamicFOVExpansion;
                                    }

                                    // Add validated enemy to detection results
                                    detectedEnemiesOutput.Add(new DetectedEnemyData
                                    {
                                        Entity = candidate.Entity,
                                        Position = chestPosition,
                                        Distance = distance,
                                        Angle = angle,
                                        IsInFOV = angle <= effectiveFOV * 0.5f,
                                        Score = CalculateScore(distance, angle, sensor)
                                    });
                                }
                            }
                        }

                        spatialCandidates.Dispose();

                        if (detectedEnemiesOutput.Length > 0)
                        {
                            // Process hybrid-detected enemies
                            ProcessDetectedEnemies(detectionTargetEntity, detectedEnemiesOutput, ecb, sensor);
                            detectedEnemiesOutput.Dispose();
                            return;
                        }
                    }

                    spatialCandidates.Dispose();
                }
            }

            // Schedule detection job
            var job = new DetectionJob
            {
                PhysicsWorld = SystemAPI.GetSingleton<PhysicsWorldSingleton>().PhysicsWorld,
                PlayerEntity = playerEntity,
                WeaponEntity = weaponEntity,
                PlayerPosition = playerPosition,
                PlayerForward = playerForward,
                FOVAngle = sensor.DetectionAngle,
                DetectionRange = sensor.DetectionRange,
                CanSwitchTarget = canSwitchTarget,
                TargetSwitchDistanceThreshold = sensor.TargetSwitchDistanceThreshold,
                MinTargetSwitchDistance = sensor.MinTargetSwitchDistance,
                TargetSwitchAngleThreshold = sensor.TargetSwitchAngleThreshold,
                DistanceWeight = aimingConfig != null ? aimingConfig.distanceWeight : 0.7f,
                AngleWeight = aimingConfig != null ? aimingConfig.angleWeight : 0.3f,
                Sensor = sensor,
                WeaponBodyPart = SystemAPI.GetComponent<WeaponBodyPartComponent>(weaponEntity),
                DetectedEnemies = detectedEnemies,
                EntityTypeHandle = GetEntityTypeHandle(),
                LocalToWorldTypeHandle = GetComponentTypeHandle<LocalToWorld>(true),
                EnemyBodyPartsTypeHandle = GetComponentTypeHandle<EnemyBodyPartsPositionComponent>(true),
                InFOVTypeHandle = GetComponentTypeHandle<InFOVTag>(true),
                CurrentTargetTypeHandle = GetComponentTypeHandle<CurrentTargetTag>(true),
                ExistingDetectedEntities = detectedEnemiesQuery.ToEntityArray(Allocator.TempJob),
                ExistingInFOVEntities = inFOVTagQuery.ToEntityArray(Allocator.TempJob),
                ExistingCurrentTargetEntities = currentTargetTagQuery.ToEntityArray(Allocator.TempJob),
                DetectionTargetEntity = detectionTargetEntity,
                EntityCommandBuffer = ecb.AsParallelWriter(),
                LocalToWorldLookup = GetComponentLookup<LocalToWorld>(true),
                EnemyBodyPartsLookup = GetComponentLookup<EnemyBodyPartsPositionComponent>(true),
                UndetectableLookup = GetComponentLookup<UndetectableTag>(true),
                DetectedEnemiesOutput = detectedEnemiesOutput
            };

            var handle = job.Schedule();
            handle.Complete();

            if (job.SwitchedTarget)
            {
                lastSwitchTime = currentTime;
            }

            // Update detection target buffer on main thread
            var buffer = GetBuffer<DetectedEnemyBuffer>(detectionTargetEntity);
            buffer.Clear();

            for (int i = 0; i < detectedEnemiesOutput.Length; i++)
            {
                var enemyData = detectedEnemiesOutput[i];
                buffer.Add(new DetectedEnemyBuffer
                {
                    Entity = enemyData.Entity,
                    Position = enemyData.Position,
                    IsInFOV = enemyData.IsInFOV,
                    Score = enemyData.Score
                });
            }

            // Update detection target component
            var currentTargetEntity = Entity.Null;
            float3 currentTargetPosition = float3.zero;
            float currentTargetScore = 0f;
            bool hasTarget = false;

            if (!currentTargetTagQuery.IsEmpty)
            {
                currentTargetEntity = currentTargetTagQuery.GetSingletonEntity();
                if (HasComponent<LocalToWorld>(currentTargetEntity))
                {
                    var transform = GetComponent<LocalToWorld>(currentTargetEntity);
                    currentTargetPosition = transform.Position;
                    hasTarget = true;
                }
            }

            // Check if the current target is in FOV
            bool isCurrentTargetInFOV = false;
            if (hasTarget && currentTargetEntity != Entity.Null)
            {
                isCurrentTargetInFOV = EntityManager.HasComponent<InFOVTag>(currentTargetEntity);
            }

            // Update detection target singleton
            SetComponent(detectionTargetEntity, new DetectionTargetComponent
            {
                CurrentTarget = currentTargetEntity,
                CurrentPosition = currentTargetPosition,
                HasTarget = hasTarget,
                IsInFOV = isCurrentTargetInFOV, // Set based on whether current target has InFOVTag
                IsDetected = !detectedEnemiesQuery.IsEmpty,
                Score = hasTarget ? currentTargetScore : 0f,
                LastKnownPosition = hasTarget ? currentTargetPosition : detectionTarget.LastKnownPosition
            });

            // --- AIM/UN-AIM STATE TRANSITION ---
            // Only un-aim if there are NO detected enemies in the detection radius
            bool shouldUnAim = detectedEnemiesQuery.IsEmpty;
            if (shouldUnAim)
            {
                if (!EntityManager.HasComponent<UnAimingTargetEventTag>(playerEntity))
                {
                    EntityManager.AddComponent<UnAimingTargetEventTag>(playerEntity);
                    Debug.Log("[AIM] No detected enemies. Transitioning to UN-AIM state.");
                }
            }
            else
            {
                if (EntityManager.HasComponent<UnAimingTargetEventTag>(playerEntity))
                {
                    EntityManager.RemoveComponent<UnAimingTargetEventTag>(playerEntity);
                    Debug.Log("[AIM] Enemy detected. Transitioning to AIM state.");
                }
            }

            // Cleanup
            ecb.Playback(EntityManager);
            ecb.Dispose();
            detectedEnemies.Dispose();
            detectedEnemiesOutput.Dispose();
        }

        private void ProcessDetectedEnemies(Entity detectionTargetEntity, NativeList<DetectedEnemyData> detectedEnemies,
            EntityCommandBuffer ecb, SphereDetectSensorComponent sensor)
        {
            // Remove DetectedTag from all currently detected entities
            var existingDetectedEntities = detectedEnemiesQuery.ToEntityArray(Allocator.TempJob);
            foreach (var entity in existingDetectedEntities)
            {
                EntityManager.RemoveComponent<DetectedTag>(entity);
            }

            // Add DetectedTag to all enemies within detection range, but respect spawn grace period
            float currentTime = (float)SystemAPI.Time.ElapsedTime;
            foreach (var enemy in detectedEnemies)
            {
                // CRITICAL: Skip dead or undetectable entities - safety check
                if (EntityManager.HasComponent<DeadTag>(enemy.Entity) ||
                    EntityManager.HasComponent<UndetectableTag>(enemy.Entity))
                {
                    Debug.Log($"<color=red>Skipping dead/undetectable entity {enemy.Entity.Index} in ProcessDetectedEnemies</color>");
                    continue;
                }

                // Check if this is a newly spawned entity
                bool isNewlySpawned = false;
                float spawnTime = 0f;

                if (EntityManager.HasComponent<SpawnComponent>(enemy.Entity))
                {
                    var spawnComponent = EntityManager.GetComponentData<SpawnComponent>(enemy.Entity);
                    spawnTime = spawnComponent.SpawnTime;
                    float timeSinceSpawn = currentTime - spawnTime;

                    // Consider entities spawned in the last 0.5 seconds as "newly spawned"
                    isNewlySpawned = timeSinceSpawn < 0.5f;

                    if (isNewlySpawned)
                    {
                        // Skip adding DetectedTag to newly spawned entities
                        // This prevents health bars from showing immediately after spawn
                        Debug.Log(
                            $"<color=yellow>Skipping detection for newly spawned entity {enemy.Entity.Index} (spawned {timeSinceSpawn:F2}s ago)</color>");
                        continue;
                    }
                }

                EntityManager.AddComponent<DetectedTag>(enemy.Entity);
            }

            // Remove existing InFOVTags
            var existingInFOVEntities = inFOVTagQuery.ToEntityArray(Allocator.TempJob);
            foreach (var entity in existingInFOVEntities)
            {
                EntityManager.RemoveComponent<InFOVTag>(entity);
            }

            // Add InFOVTag to entities within FOV
            foreach (var enemy in detectedEnemies)
            {
                if (enemy.IsInFOV)
                {
                    EntityManager.AddComponent<InFOVTag>(enemy.Entity);
                }
            }

            // Get current target if exists
            Entity currentTarget = Entity.Null;
            float currentTargetScore = float.MinValue;
            float3 currentTargetPosition = float3.zero;
            bool hasCurrentTarget = false;

            if (!currentTargetTagQuery.IsEmpty)
            {
                currentTarget = currentTargetTagQuery.GetSingletonEntity();
                // Find current target's score and position
                for (int i = 0; i < detectedEnemies.Length; i++)
                {
                    if (detectedEnemies[i].Entity == currentTarget)
                    {
                        currentTargetScore = detectedEnemies[i].Score;
                        currentTargetPosition = detectedEnemies[i].Position;
                        hasCurrentTarget = true;
                        break;
                    }
                }

                // If current target is not in detected enemies, get its position from LocalToWorld
                if (!hasCurrentTarget && HasComponent<LocalToWorld>(currentTarget))
                {
                    currentTargetPosition = GetComponent<LocalToWorld>(currentTarget).Position;
                    hasCurrentTarget = true;
                }
            }

            // Find the best target based on score
            Entity bestTarget = Entity.Null;
            float bestScore = float.MinValue;
            float3 bestTargetPosition = float3.zero;

            foreach (var enemy in detectedEnemies)
            {
                if (enemy.Score > bestScore)
                {
                    bestScore = enemy.Score;
                    bestTarget = enemy.Entity;
                    bestTargetPosition = enemy.Position;
                }
            }

            // Determine if we should switch targets
            bool shouldSwitchTarget = false;
            if (bestTarget != Entity.Null)
            {
                if (currentTarget == Entity.Null)
                {
                    shouldSwitchTarget = true; // No current target, switch to best target
                }
                else if (currentTarget != bestTarget)
                {
                    // Switch only if the new target is significantly better
                    float scoreDifference = bestScore - currentTargetScore;
                    if (scoreDifference > sensor.TargetSwitchScoreThreshold)
                    {
                        shouldSwitchTarget = true;
                    }
                }
            }

            // Update current target if we should switch
            if (shouldSwitchTarget || (currentTarget == Entity.Null && bestTarget != Entity.Null))
            {
                // Remove current target tag directly
                var currentTargets = currentTargetTagQuery.ToEntityArray(Allocator.TempJob);
                foreach (var entity in currentTargets)
                {
                    EntityManager.RemoveComponent<CurrentTargetTag>(entity);
                }

                // Add tag to new best target directly
                EntityManager.AddComponent<CurrentTargetTag>(bestTarget);

                // Update tracking variables
                currentTarget = bestTarget;
                currentTargetPosition = bestTargetPosition;
                currentTargetScore = bestScore;
                hasCurrentTarget = true;
            }

            // Check if the current target is in FOV
            bool isCurrentTargetInFOV = false;
            if (hasCurrentTarget && currentTarget != Entity.Null)
            {
                // Check if the current target has the InFOVTag
                for (int i = 0; i < detectedEnemies.Length; i++)
                {
                    if (detectedEnemies[i].Entity == currentTarget)
                    {
                        isCurrentTargetInFOV = detectedEnemies[i].IsInFOV;
                        break;
                    }
                }
            }

            // Always update DetectionTargetComponent with latest target info
            SetComponent(detectionTargetEntity, new DetectionTargetComponent
            {
                CurrentTarget = currentTarget,
                CurrentPosition = currentTargetPosition,
                HasTarget = hasCurrentTarget,
                IsInFOV = isCurrentTargetInFOV, // Set based on whether current target is in FOV
                IsDetected = !detectedEnemiesQuery.IsEmpty,
                Score = currentTargetScore,
                LastKnownPosition = hasCurrentTarget
                    ? currentTargetPosition
                    : GetComponent<DetectionTargetComponent>(detectionTargetEntity).LastKnownPosition
            });

            // Always update the buffer with all detected enemies
            var buffer = GetBuffer<DetectedEnemyBuffer>(detectionTargetEntity);
            buffer.Clear();

            // Sort enemies by score before adding to buffer
            var sortedEnemies = new NativeArray<DetectedEnemyData>(detectedEnemies.Length, Allocator.Temp);
            for (int i = 0; i < detectedEnemies.Length; i++)
            {
                sortedEnemies[i] = detectedEnemies[i];
            }

            sortedEnemies.Sort(new DetectedEnemyComparer());

            foreach (var enemy in sortedEnemies)
            {
                buffer.Add(new DetectedEnemyBuffer
                {
                    Entity = enemy.Entity,
                    Position = enemy.Position,
                    IsInFOV = enemy.IsInFOV,
                    Score = enemy.Score
                });
            }

            sortedEnemies.Dispose();
        }

        private struct DetectedEnemyComparer : IComparer<DetectedEnemyData>
        {
            public int Compare(DetectedEnemyData x, DetectedEnemyData y)
            {
                return y.Score.CompareTo(x.Score); // Sort in descending order
            }
        }

        private float CalculateScore(float distance, float angle, SphereDetectSensorComponent sensor)
        {
            // Use configuration values for distance and angle weights
            float distanceWeight = aimingConfig != null ? aimingConfig.distanceWeight : 0.7f;
            float angleWeight = aimingConfig != null ? aimingConfig.angleWeight : 0.3f;

            float distanceScore = 1.0f - (distance / sensor.DetectionRange);
            float angleScore = 1.0f - (angle / sensor.DetectionAngle);

            // Add extra bonus for very close enemies (within 20% of detection range)
            float proximityBonus = distance < (sensor.DetectionRange * 0.2f) ? 0.3f : 0.0f;

            return (distanceScore * distanceWeight) + (angleScore * angleWeight) + proximityBonus;
        }

        [BurstCompile]
        private struct DetectionJob : IJob
        {
            [ReadOnly] public PhysicsWorld PhysicsWorld;
            [ReadOnly] public Entity PlayerEntity;
            [ReadOnly] public Entity WeaponEntity;
            [ReadOnly] public Entity DetectionTargetEntity;
            [ReadOnly] public float3 PlayerPosition;
            [ReadOnly] public float3 PlayerForward;
            [ReadOnly] public float FOVAngle;
            [ReadOnly] public float DetectionRange;
            [ReadOnly] public bool CanSwitchTarget;
            [ReadOnly] public float TargetSwitchDistanceThreshold;
            [ReadOnly] public float MinTargetSwitchDistance;
            [ReadOnly] public float TargetSwitchAngleThreshold;
            [ReadOnly] public float DistanceWeight;
            [ReadOnly] public float AngleWeight;
            [ReadOnly] public SphereDetectSensorComponent Sensor;
            [ReadOnly] public WeaponBodyPartComponent WeaponBodyPart;
            [ReadOnly] public NativeArray<ArchetypeChunk> DetectedEnemies;
            [ReadOnly] public EntityTypeHandle EntityTypeHandle;
            [ReadOnly] public ComponentTypeHandle<LocalToWorld> LocalToWorldTypeHandle;
            [ReadOnly] public ComponentTypeHandle<EnemyBodyPartsPositionComponent> EnemyBodyPartsTypeHandle;
            [ReadOnly] public ComponentTypeHandle<InFOVTag> InFOVTypeHandle;
            [ReadOnly] public ComponentTypeHandle<CurrentTargetTag> CurrentTargetTypeHandle;
            [ReadOnly] public NativeArray<Entity> ExistingDetectedEntities;
            [ReadOnly] public NativeArray<Entity> ExistingInFOVEntities;
            [ReadOnly] public NativeArray<Entity> ExistingCurrentTargetEntities;
            [ReadOnly] public ComponentLookup<LocalToWorld> LocalToWorldLookup;
            [ReadOnly] public ComponentLookup<EnemyBodyPartsPositionComponent> EnemyBodyPartsLookup;
            [ReadOnly] public ComponentLookup<UndetectableTag> UndetectableLookup;

            public EntityCommandBuffer.ParallelWriter EntityCommandBuffer;
            public bool SwitchedTarget;
            public NativeList<DetectedEnemyData> DetectedEnemiesOutput;

            public void Execute()
            {
                // Remove existing tags first
                for (int i = 0; i < ExistingDetectedEntities.Length; i++)
                    EntityCommandBuffer.RemoveComponent<DetectedTag>(0, ExistingDetectedEntities[i]);

                for (int i = 0; i < ExistingInFOVEntities.Length; i++)
                    EntityCommandBuffer.RemoveComponent<InFOVTag>(0, ExistingInFOVEntities[i]);

                for (int i = 0; i < ExistingCurrentTargetEntities.Length; i++)
                    EntityCommandBuffer.RemoveComponent<CurrentTargetTag>(0, ExistingCurrentTargetEntities[i]);

                // Create NativeList for storing detected enemies
                var detectedEnemiesData = new NativeList<EnemyDetectionData>(Allocator.Temp);
                var inFOVEnemiesData = new NativeList<EnemyDetectionData>(Allocator.Temp);

                // SphereCastAll for initial detection
                var collectorCount = 100; // Support for up to 1000 enemies
                var collector = new NativeList<ColliderCastHit>(collectorCount, Allocator.Temp);
                var filter = new CollisionFilter
                {
                    BelongsTo = Sensor.BelongsTo,
                    CollidesWith = Sensor.CollidesWith
                };

                PhysicsWorld.SphereCastAll(
                    PlayerPosition,
                    DetectionRange,
                    float3.zero,
                    0.01f, // Small movement distance
                    ref collector,
                    filter
                );

                // Get current target if exists
                Entity currentTarget = ExistingCurrentTargetEntities.Length > 0
                    ? ExistingCurrentTargetEntities[0]
                    : Entity.Null;

                // Process detected enemies
                for (int i = 0; i < collector.Length; i++)
                {
                    var hit = collector[i];
                    var hitEntity = PhysicsWorld.Bodies[hit.RigidBodyIndex].Entity;

                    // Skip if missing required components or if entity is undetectable
                    if (!LocalToWorldLookup.HasComponent(hitEntity) ||
                        !EnemyBodyPartsLookup.HasComponent(hitEntity) ||
                        UndetectableLookup.HasComponent(hitEntity))
                        continue;

                    var enemyTransform = LocalToWorldLookup[hitEntity];
                    var enemyBodyParts = EnemyBodyPartsLookup[hitEntity];
                    var enemyPosition = enemyBodyParts.ChestPosition;
                    var directionToEnemy = math.normalize(enemyPosition - PlayerPosition);
                    var angleToEnemy = math.degrees(math.acos(math.dot(directionToEnemy, PlayerForward)));
                    var distanceToEnemy = math.distance(PlayerPosition, enemyPosition);

                    // Add to detected enemies
                    EntityCommandBuffer.AddComponent<DetectedTag>(0, hitEntity);

                    // --- AAA Dynamic FOV and Sticky Targeting ---
                    float effectiveFOV = FOVAngle;
                    bool isSticky = false;

                    // Check if this is the current target for sticky targeting
                    if (hitEntity == currentTarget)
                    {
                        if (angleToEnemy <= FOVAngle * 0.5f + Sensor.stickyAngle &&
                            distanceToEnemy <= DetectionRange + Sensor.stickyDistance)
                        {
                            effectiveFOV += Sensor.dynamicFOVExpansion;
                            isSticky = true;
                        }
                    }
                    // Expand FOV if enemy is near the edge
                    else if (angleToEnemy > FOVAngle * 0.5f - Sensor.dynamicFOVExpansion)
                    {
                        effectiveFOV += Sensor.dynamicFOVExpansion;
                    }

                    // Calculate score using the same logic as KDTree path
                    float distanceScore = 1.0f - (distanceToEnemy / DetectionRange);
                    float angleScore = 1.0f - (angleToEnemy / FOVAngle);
                    float proximityBonus = distanceToEnemy < (DetectionRange * 0.2f) ? 0.3f : 0.0f;
                    float score = (distanceScore * DistanceWeight) + (angleScore * AngleWeight) + proximityBonus;

                    // Apply hysteresis for current target (bonus to keep current target)
                    if (hitEntity == currentTarget)
                    {
                        score += 0.1f; // Small bonus to favor keeping current target
                    }

                    var enemyData = new EnemyDetectionData
                    {
                        Entity = hitEntity,
                        Position = enemyPosition,
                        Distance = distanceToEnemy,
                        Angle = angleToEnemy,
                        Score = score,
                        DistanceWeight = DistanceWeight,
                        AngleWeight = AngleWeight
                    };

                    detectedEnemiesData.Add(enemyData);

                    // Check if in FOV using effective FOV
                    bool isInFOV = angleToEnemy <= effectiveFOV * 0.5f;

                    // Store for buffer update
                    DetectedEnemiesOutput.Add(new DetectedEnemyData
                    {
                        Entity = hitEntity,
                        Position = enemyPosition,
                        Distance = distanceToEnemy,
                        Angle = angleToEnemy,
                        IsInFOV = isInFOV,
                        Score = score
                    });

                    // Check if in FOV
                    if (isInFOV)
                    {
                        EntityCommandBuffer.AddComponent<InFOVTag>(0, hitEntity);
                        inFOVEnemiesData.Add(enemyData);
                    }
                }

                // Sort all detected enemies by distance and angle for closest target selection
                detectedEnemiesData.Sort(new EnemyComparer());

                // Find best target with stability (highest score = best target)
                Entity bestTarget = Entity.Null;
                float bestScore = float.MinValue; // Changed to MinValue to find highest score

                float3 currentTargetPos = currentTarget != Entity.Null && LocalToWorldLookup.HasComponent(currentTarget)
                    ? LocalToWorldLookup[currentTarget].Position
                    : float3.zero;

                // Target selection with hysteresis (consider all detected enemies, not just FOV)
                for (int i = 0; i < detectedEnemiesData.Length; i++)
                {
                    var enemyData = detectedEnemiesData[i];

                    // Check if this is a better target (higher score = better)
                    if (enemyData.Score > bestScore)
                    {
                        // If we have a current target, apply switching thresholds
                        if (currentTarget != Entity.Null && !CanSwitchTarget)
                        {
                            float distanceToCurrentTarget = math.distance(enemyData.Position, currentTargetPos);

                            // Only switch if the new target is significantly better
                            if (distanceToCurrentTarget > MinTargetSwitchDistance &&
                                enemyData.Score > bestScore + TargetSwitchDistanceThreshold)
                            {
                                bestScore = enemyData.Score;
                                bestTarget = enemyData.Entity;
                                SwitchedTarget = true;
                            }
                        }
                        else
                        {
                            bestScore = enemyData.Score;
                            bestTarget = enemyData.Entity;
                            SwitchedTarget = currentTarget != bestTarget;
                        }
                    }

                    detectedEnemiesData[i] = enemyData;
                }

                // Set current target
                if (bestTarget != Entity.Null)
                {
                    EntityCommandBuffer.AddComponent<CurrentTargetTag>(0, bestTarget);
                }

                // Cleanup
                collector.Dispose();
                detectedEnemiesData.Dispose();
                inFOVEnemiesData.Dispose();
            }

            private struct EnemyDetectionData : IEquatable<EnemyDetectionData>, IComparable<EnemyDetectionData>
            {
                public Entity Entity;
                public float3 Position;
                public float Distance;
                public float Angle;
                public float Score;
                public float DistanceWeight;
                public float AngleWeight;

                public bool Equals(EnemyDetectionData other)
                {
                    return Entity == other.Entity;
                }

                public int CompareTo(EnemyDetectionData other)
                {
                    // Sort by actual score in descending order (highest score first)
                    return other.Score.CompareTo(Score);
                }
            }

            private struct EnemyComparer : IComparer<EnemyDetectionData>
            {
                public int Compare(EnemyDetectionData a, EnemyDetectionData b)
                {
                    return a.CompareTo(b);
                }

            }
        }

        private void OnUpdateConeAngleEventHandler(Events.OnChangeConeAngleEvent evt)
        {
            // Update all player entities with SphereDetectSensorComponent
            Entities.WithAll<PlayerTag, SphereDetectSensorComponent>().ForEach(
                (Entity entity, ref SphereDetectSensorComponent sensor) =>
                {
                    sensor.DetectionAngle = evt.DetectionAngle;
                }).WithStructuralChanges().Run();
        }
        
        /// <summary>
        /// Handles updates to ECS detection parameters from the AimingModule/AimingConfiguration (event-driven, per rules.md)
        /// </summary>
        private void OnUpdateDetectionParametersEventHandler(Events.OnUpdateDetectionParametersEvent evt)
        {
            // Update all player entities with SphereDetectSensorComponent
            Entities.WithAll<PlayerTag, SphereDetectSensorComponent>().ForEach(
                (Entity entity, ref SphereDetectSensorComponent sensor) =>
                {
                    // FOV and detection parameters

                    // Target selection and stability (if present in event)
                    sensor.TargetSwitchDistanceThreshold = evt.TargetSwitchDistanceThreshold;
                    sensor.MinTargetSwitchDistance = evt.MinTargetSwitchDistance;
                    sensor.TargetSwitchAngleThreshold = evt.TargetSwitchAngleThreshold;
                    sensor.TargetSwitchScoreThreshold = evt.TargetSwitchScoreThreshold;

                    // AAA Aim Assist
                    sensor.dynamicFOVExpansion = evt.DynamicFOVExpansion;
                    sensor.stickyAngle = evt.StickyAngle;
                    sensor.stickyDistance = evt.StickyDistance;
                    sensor.aimAssistSpeed = evt.AimAssistSpeed;
                }).WithStructuralChanges().Run();
        }
    }
}
