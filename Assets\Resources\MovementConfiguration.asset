%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f426e1b6b7ff4f42be4889a0c4e1387, type: 3}
  m_Name: MovementConfiguration
  m_EditorClassIdentifier: 
  improvedMovement: 1
  useRootMotionForTurning: 0
  accelerationTime: 0.1
  decelerationTime: 0.2
  aimingSpeedMultiplier: 0.8
  strafingSpeedMultiplier: 0.9
  backpedalSpeedMultiplier: 0.7
  directionChangeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  moveSpeed: 1
  rotationSpeed: 720
  targetRotationSpeed: 2
  minRotationAngle: 45
  useWholeBodyRotation: 1
  rotationSmoothTime: 0.25
  maxRotationSpeed: 180
  useInPlaceRotation: 0
  rotationCooldown: 0.2
  rotationStabilityThreshold: 0.5
  fastRotationSpeed: 0.2
  waitForStop: 0.5
  transitionCooldown: 0.25
  improvedTransitionCooldown: 0.1
