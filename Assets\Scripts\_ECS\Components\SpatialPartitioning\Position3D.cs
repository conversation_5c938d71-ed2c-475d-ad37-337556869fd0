using Unity.Mathematics;
using System;
using Unity.Entities;
using GimmeDOTSGeometry;

namespace PlayerFAP.Components.SpatialPartitioning
{
    public struct Position3D : IPosition3D, IEquatable<Position3D>
    {
        public float3 GetPosition() => Position;

        public bool Equals(Position3D other)
        {
            return Position.Equals(other.Position);
        }

        public override bool Equals(object obj)
        {
            return obj is Position3D other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Position.GetHashCode();
        }

        public float3 Position { get; set; }
    }

    /// <summary>
    /// Enhanced position data that includes entity reference for efficient hybrid detection
    /// Following rules.md: Performance-optimized structure for KDTree + SphereCast hybrid approach
    /// </summary>
    public struct EnemyPositionData : IPosition3D, IEquatable<EnemyPositionData>
    {
        public float3 Position { get; set; }
        public Entity Entity;
        public int EntityIndex; // For faster array lookups
        public float LastUpdateTime; // For staleness detection

        public EnemyPositionData(float3 position, Entity entity, int entityIndex, float updateTime)
        {
            Position = position;
            Entity = entity;
            EntityIndex = entityIndex;
            LastUpdateTime = updateTime;
        }

        public bool Equals(EnemyPositionData other)
        {
            return Entity.Equals(other.Entity);
        }

        public override bool Equals(object obj)
        {
            return obj is EnemyPositionData other && Equals(other);
        }

        public override int GetHashCode()
        {
            return Entity.GetHashCode();
        }
    }
}
