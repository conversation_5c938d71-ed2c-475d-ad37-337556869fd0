using GimmeDOTSGeometry;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;

namespace PlayerFAP.Components.SpatialPartitioning
{
    /// <summary>
    /// Enhanced KDTree component for hybrid detection approach
    /// Following rules.md: Performance-optimized spatial partitioning
    /// </summary>
    public struct KDTreeComponent : IComponentData
    {
        public Native3DKDTree<EnemyPositionData> HybridTree; // New enhanced tree
        public Native3DKDTree<GimmeDOTSGeometry.Position3D> Tree; // Legacy tree for compatibility
        public float SearchRadius;
        public bool IsInitialized;
        public bool UseHybridTree; // Flag to use enhanced tree
        public float LastUpdateTime;
        public int EnemyCount; // For performance tracking
    }

    public struct KDTreeSearchResult : IBufferElementData
    {
        public Entity Entity;
        public float3 Position;
        public float Distance;
        public int EntityIndex; // For faster lookups
    }

    /// <summary>
    /// Hybrid search result with entity reference for optimal performance
    /// </summary>
    public struct HybridKDTreeResult : IBufferElementData
    {
        public EnemyPositionData EnemyData;
        public float Distance;
        public bool RequiresPhysicsValidation;
    }
}
