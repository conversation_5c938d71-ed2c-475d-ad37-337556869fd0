using UnityEngine;

namespace PlayerFAP.Configuration
{
    /// <summary>
    /// Performance optimization configuration for managing system update frequencies
    /// and memory allocation patterns to achieve 60fps with 150+ enemies
    /// </summary>
    [CreateAssetMenu(fileName = "PerformanceOptimizationConfig", menuName = "PlayerFAP/Performance Optimization Config")]
    public class PerformanceOptimizationConfig : ScriptableObject
    {
        [Header("Detection System Performance")]
        [Tooltip("Detection system update frequency (Hz). Lower = better performance")]
        [Range(5f, 60f)]
        public float detectionUpdateFrequency = 15f;
        
        [Tooltip("Maximum enemies to process per detection frame")]
        [Range(10, 100)]
        public int maxEnemiesPerDetectionFrame = 50;
        
        [Tooltip("Distance-based LOD threshold. Enemies beyond this distance update less frequently")]
        [Range(10f, 50f)]
        public float lodDistanceThreshold = 25f;
        
        [Header("KDTree Optimization")]
        [Tooltip("Minimum enemy count change to trigger KDTree rebuild")]
        [Range(5, 20)]
        public int kdTreeRebuildThreshold = 10;
        
        [Tooltip("KDTree staleness time before forced rebuild (seconds)")]
        [Range(1f, 5f)]
        public float kdTreeStalenessTime = 2f;
        
        [Tooltip("Player movement distance threshold to trigger KDTree rebuild")]
        [Range(2f, 10f)]
        public float playerMovementThreshold = 5f;
        
        [Header("Health Bar Performance")]
        [Tooltip("Health bar update frequency (Hz)")]
        [Range(10f, 60f)]
        public float healthBarUpdateFrequency = 20f;
        
        [Tooltip("Grace period before hiding health bars (seconds)")]
        [Range(0.1f, 2f)]
        public float healthBarHideGracePeriod = 0.3f;
        
        [Tooltip("Maximum health bars to process per frame")]
        [Range(5, 50)]
        public int maxHealthBarsPerFrame = 25;
        
        [Header("Memory Management")]
        [Tooltip("Pre-allocate NativeArray capacity for enemies")]
        [Range(50, 500)]
        public int preAllocatedEnemyCapacity = 200;
        
        [Tooltip("Batch size for parallel job processing")]
        [Range(16, 128)]
        public int jobBatchSize = 32;
        
        [Tooltip("Enable aggressive garbage collection")]
        public bool enableAggressiveGC = true;
        
        [Header("Adaptive Quality")]
        [Tooltip("Target FPS threshold for adaptive quality")]
        [Range(30f, 60f)]
        public float targetFpsThreshold = 45f;
        
        [Tooltip("Reduce detection range when FPS drops")]
        [Range(0.5f, 1f)]
        public float fpsBasedRangeMultiplier = 0.75f;
        
        [Tooltip("Maximum enemy count before culling distant enemies")]
        [Range(100, 300)]
        public int maxEnemyCount = 150;
        
        [Header("System Update Intervals")]
        [Tooltip("Animation system update every N frames (1 = every frame)")]
        [Range(1, 3)]
        public int animationUpdateInterval = 1;
        
        [Tooltip("UI system update every N frames")]
        [Range(2, 10)]
        public int uiUpdateInterval = 3;
        
        [Tooltip("Physics validation update every N frames")]
        [Range(1, 5)]
        public int physicsValidationInterval = 2;
        
        [Header("Debug & Monitoring")]
        [Tooltip("Enable performance monitoring")]
        public bool enablePerformanceMonitoring = true;
        
        [Tooltip("Log performance warnings when FPS drops")]
        public bool logPerformanceWarnings = true;
        
        [Tooltip("Performance log interval (seconds)")]
        [Range(1f, 10f)]
        public float performanceLogInterval = 5f;
        
        /// <summary>
        /// Get the current detection update interval in frames
        /// </summary>
        public int GetDetectionUpdateInterval()
        {
            return Mathf.Max(1, Mathf.RoundToInt(60f / detectionUpdateFrequency));
        }
        
        /// <summary>
        /// Get the current health bar update interval in frames
        /// </summary>
        public int GetHealthBarUpdateInterval()
        {
            return Mathf.Max(1, Mathf.RoundToInt(60f / healthBarUpdateFrequency));
        }
        
        /// <summary>
        /// Check if adaptive quality should reduce performance based on current FPS
        /// </summary>
        public bool ShouldReduceQuality(float currentFps)
        {
            return currentFps < targetFpsThreshold;
        }
        
        /// <summary>
        /// Get the effective detection range based on current performance
        /// </summary>
        public float GetEffectiveDetectionRange(float baseRange, float currentFps)
        {
            if (ShouldReduceQuality(currentFps))
            {
                return baseRange * fpsBasedRangeMultiplier;
            }
            return baseRange;
        }
        
        /// <summary>
        /// Get the effective enemy processing count based on current performance
        /// </summary>
        public int GetEffectiveEnemyProcessingCount(float currentFps)
        {
            if (ShouldReduceQuality(currentFps))
            {
                return Mathf.RoundToInt(maxEnemiesPerDetectionFrame * fpsBasedRangeMultiplier);
            }
            return maxEnemiesPerDetectionFrame;
        }
        
        private void OnValidate()
        {
            // Ensure reasonable values
            detectionUpdateFrequency = Mathf.Clamp(detectionUpdateFrequency, 5f, 60f);
            healthBarUpdateFrequency = Mathf.Clamp(healthBarUpdateFrequency, 10f, 60f);
            kdTreeRebuildThreshold = Mathf.Clamp(kdTreeRebuildThreshold, 5, 20);
            maxEnemiesPerDetectionFrame = Mathf.Clamp(maxEnemiesPerDetectionFrame, 10, 100);
            jobBatchSize = Mathf.Clamp(jobBatchSize, 16, 128);
        }
    }
}
