%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Color
  m_Shader: {fileID: 4800000, guid: 00487d633a303b14eb97c1d6b2474c15, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AoTex:
        m_Texture: {fileID: 2800000, guid: 06668bb4c82d3bf4b8118d1058653525, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Control:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Detail:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Diffuse:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emissive:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowExtrusion_Texture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GravelTex:
        m_Texture: {fileID: 2800000, guid: 337edc49d30b11e49856b9a67be2b0de, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightMap:
        m_Texture: {fileID: 2800000, guid: 9e2f4260e3bf7924ba83a01925a5701c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: e216507594e1430429e3408fae441d68, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OverlayTex:
        m_Texture: {fileID: 2800000, guid: 06668bb4c82d3bf4b8118d1058653525, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RockTex:
        m_Texture: {fileID: 2800000, guid: 58395135417f84145a51ddc3161f5525, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SandTex:
        m_Texture: {fileID: 2800000, guid: e216507594e1430429e3408fae441d68, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Specular:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Thickness:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Amount: 0.0001
    - _Blend1: 0
    - _BumpScale: 1
    - _Corrective_Glow: 1
    - _Cull: 2
    - _Cutoff: 0.18
    - _DetailNormalMapScale: 1
    - _Distortion: 0
    - _DstBlend: 0
    - _EmissionScaleUI: 0
    - _Emission_Intensity: 1
    - _Emissive_OnOff: 0
    - _Fresnel_Width: 1.5
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GridOffsetX: 0
    - _GridOffsetY: 0
    - _GridSpacing: 10
    - _GridSpacingX: 1
    - _GridSpacingY: 1
    - _GridThickness: 0.1
    - _LineWidth: 0.01
    - _Magnitude: 0.799
    - _Metallic: 0
    - _Mode: 0
    - _Normal_Intensity: 1
    - _OcclusionStrength: 1
    - _Outline: 0.005
    - _Outline_Width: 0.05
    - _Pan_Speed_X: 1
    - _Pan_Speed_Y: 1
    - _Parallax: 0.02
    - _Power: 1
    - _Pulse_Speed: 6
    - _Radius: 0.5
    - _Scale: 1
    - _Shininess: 0.01
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _Specular_Power: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _WindQuality: 5
    - _ZWrite: 1
    - _emision: 0
    - _gNumTiles: 46.2
    - _gThreshhold: 0.506
    - _tileX: 10
    - _tileY: 5
    m_Colors:
    - _BaseColour: {r: 1, g: 0.03676468, b: 0.03676468, a: 0}
    - _Center: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Colour: {r: 1, g: 1, b: 1, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColorUI: {r: 1, g: 1, b: 1, a: 1}
    - _Fresnel_Color: {r: 1, g: 0.6, b: 0, a: 1}
    - _GridColor: {r: 1, g: 1, b: 1, a: 0}
    - _GridColour: {r: 0.19442041, g: 0.42647058, b: 0.42647058, a: 1}
    - _Highlight_Color: {r: 1, g: 0.6, b: 0, a: 1}
    - _HueVariation: {r: 1, g: 0.5, b: 0, a: 0.1}
    - _LineColor: {r: 0.4705882, g: 0.089965396, b: 0.089965396, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _Outline_Color: {r: 1, g: 0.6, b: 0, a: 1}
    - _SpecColor: {r: 1, g: 0, b: 0, a: 1}
    - _SubColor: {r: 1, g: 1, b: 1, a: 1}
    - _gEdgeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
