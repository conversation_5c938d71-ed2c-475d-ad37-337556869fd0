using UnityEngine;
using Sirenix.OdinInspector;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Configuration for the aiming system, following the Scriptable Object pattern
    /// as specified in rules.md
    /// </summary>
    [CreateAssetMenu(fileName = "AimingConfiguration", menuName = "Configuration/AimingConfiguration")]
    public class AimingConfiguration : ScriptableObject
    {
        [Header("FOV Parameters")]
        [Toolt<PERSON>("Angle of the detection cone in degrees")]
        public float coneAngleBeforeAiming = 45f;
        public float coneAngleAfterAiming = 45f;

        [Tooltip("Range of the detection cone")]
        public float coneRange = 10f;

        [Tooltip("Radius of the detection sphere")]
        public float detectionRadius = 6f;

        [Header("Target Selection")]
        [Tooltip("Whether to allow targeting enemies outside the player's field of view")]
        public bool allowNonFOVTargets = false;

        [Tooltip("Base distance for normalization in target selection")]
        public float baseDistanceThreshold = 10f;

        [Tooltip("Base angle for normalization in target selection")]
        public float baseAngleThreshold = 45f;

        [Tooltip("Weight for distance in scoring")]
        [Range(0f, 1f)]
        public float distanceWeight = 0.7f;

        [Tooltip("Weight for angle in scoring")]
        [Range(0f, 1f)]
        public float angleWeight = 0.3f;

        [Tooltip("Prevents rapid switching between close targets")]
        [Range(0f, 1f)]
        public float targetSwitchHysteresis = 0.15f;

        [Header("Target Switching Parameters")]
        [Tooltip("Distance threshold for switching targets")] 
        public float TargetSwitchDistanceThreshold = 2.0f;
        [Tooltip("Minimum distance required to allow target switching")] 
        public float MinTargetSwitchDistance = 1.0f;
        [Tooltip("Angle threshold for switching targets (degrees)")]
        public float TargetSwitchAngleThreshold = 25.0f;
        [Tooltip("Score threshold for switching targets")]
        public float TargetSwitchScoreThreshold = 0.5f;

        [Header("AAA Aim Assist")]
        [Tooltip("Degrees to temporarily expand FOV when a target is near the edge")] public float dynamicFOVExpansion = 15f;
        [Tooltip("Angle in degrees for sticky targeting")] public float stickyAngle = 10f;
        [Tooltip("Distance in meters for sticky targeting")] public float stickyDistance = 2.5f;
        [Tooltip("Degrees per second for auto-rotation toward target")] public float aimAssistSpeed = 120f;

        [Tooltip("How quickly to interpolate to a new target")]
        [Range(0.01f, 1f)]
        public float targetInterpolationSpeed = 0.15f;

        [Header("Cooldown Settings")]
        [Tooltip("Time in seconds to wait before allowing aiming again after losing all enemies")]
        public float CooldownAfterLostAllEnenies = 1.0f;

        [Header("Ragdoll Aiming Controller Settings")]
        [Tooltip("Smooth time for target switching")]
        public float targetSwitchSmoothTime = 0.3f;

        [Tooltip("Smooth time for weight changes")]
        public float weightSmoothTime = 0.3f;

        [Tooltip("Maximum radians delta for smooth rotation")]
        public float maxRadiansDelta = 3f;

        [Tooltip("Maximum magnitude delta for smooth rotation")]
        public float maxMagnitudeDelta = 3f;

        [Tooltip("Slerp speed for rotation")]
        public float slerpSpeed = 3f;

        [Tooltip("Smooth damp time")]
        public float smoothDampTime = 0f;

        [Tooltip("Minimum distance for aiming")]
        public float minDistance = 1f;

        [Tooltip("Maximum root angle for aiming")]
        public float maxRootAngle = 45f;

        [Tooltip("Turn to target time")]
        public float turnToTargetTime = 0.2f;



        [Header("ECS Detection Default Values")]
        [Tooltip("Default detection radius for ECS systems")]
        public float ecsDetectionRadius = 6f;

        [Tooltip("Default detection range for ECS systems")]
        public float ecsDetectionRange = 20f;

        [Tooltip("Default detection angle for ECS systems")]
        public float ecsDetectionAngle = 45f;

        [Tooltip("Cooldown time for detection")]
        public float detectionCooldownTime = 1.0f;

        [Header("Speed Controller Settings")]
        [Tooltip("Animation curve for aiming speed modifiers")]
        public AnimationCurve aimingSpeedCurve = AnimationCurve.Linear(0, 0.8f, 1, 0.6f);

        [Tooltip("Animation curve for strafing speed modifiers")]
        public AnimationCurve strafingSpeedCurve = AnimationCurve.Linear(0, 1.0f, 1, 0.85f);

        [Tooltip("Animation curve for backpedal speed modifiers")]
        public AnimationCurve backpedalSpeedCurve = AnimationCurve.Linear(0, 1.0f, 1, 0.7f);

        [Tooltip("Animation curve for target distance speed control")]
        public AnimationCurve targetDistanceSpeedCurve = AnimationCurve.EaseInOut(0, 0.3f, 10, 1.0f);

        [Tooltip("Minimum target distance for speed calculations")]
        public float speedMinTargetDistance = 1.0f;

        [Tooltip("Maximum target distance for speed calculations")]
        public float speedMaxTargetDistance = 10.0f;

        [Tooltip("Forward movement threshold")]
        public float forwardThreshold = 0.7f;

        [Tooltip("Backward movement threshold")]
        public float backwardThreshold = -0.3f;

        [Tooltip("Strafe movement threshold")]
        public float strafeThreshold = 0.7f;

        [Header("Debug")]
        [Tooltip("Show debug information about target selection")]
        public bool showTargetDebug = false;
    }
}
