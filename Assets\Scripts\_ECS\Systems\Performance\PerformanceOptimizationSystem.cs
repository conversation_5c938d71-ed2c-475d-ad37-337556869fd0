using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using PlayerFAP.Configuration;

namespace PlayerFAP.Systems.Performance
{
    /// <summary>
    /// System that monitors performance and applies adaptive optimizations
    /// to maintain target FPS with large enemy counts
    /// </summary>
    [BurstCompile]
    public partial struct PerformanceOptimizationSystem : ISystem
    {
        private float lastPerformanceLogTime;
        private float frameTimeAccumulator;
        private int frameCount;
        private bool isInitialized;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            lastPerformanceLogTime = 0f;
            frameTimeAccumulator = 0f;
            frameCount = 0;
            isInitialized = false;
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!isInitialized)
            {
                Initialize(ref state);
                isInitialized = true;
            }
            
            // Get performance configuration
            var config = GetPerformanceConfig();
            if (config == null) return;
            
            // Calculate current FPS
            float deltaTime = SystemAPI.Time.DeltaTime;
            frameTimeAccumulator += deltaTime;
            frameCount++;
            
            float currentTime = (float)SystemAPI.Time.ElapsedTime;
            
            // Log performance every interval
            if (currentTime - lastPerformanceLogTime >= config.performanceLogInterval)
            {
                float averageFps = frameCount / frameTimeAccumulator;
                
                if (config.logPerformanceWarnings && averageFps < config.targetFpsThreshold)
                {
                    Debug.LogWarning($"[PerformanceOptimization] FPS below target: {averageFps:F1} < {config.targetFpsThreshold}");
                }
                
                if (config.enablePerformanceMonitoring)
                {
                    Debug.Log($"[PerformanceOptimization] Average FPS: {averageFps:F1}, Frame Time: {(frameTimeAccumulator / frameCount * 1000):F2}ms");
                }
                
                // Reset counters
                lastPerformanceLogTime = currentTime;
                frameTimeAccumulator = 0f;
                frameCount = 0;
                
                // Trigger garbage collection if enabled and FPS is low
                if (config.enableAggressiveGC && averageFps < config.targetFpsThreshold)
                {
                    System.GC.Collect();
                    System.GC.WaitForPendingFinalizers();
                    System.GC.Collect();
                }
            }
        }
        
        private void Initialize(ref SystemState state)
        {
            // Pre-warm systems and allocate memory pools
            var config = GetPerformanceConfig();
            if (config == null) return;
            
            Debug.Log($"[PerformanceOptimization] Initializing with capacity: {config.preAllocatedEnemyCapacity}");
        }
        
        private PerformanceOptimizationConfig GetPerformanceConfig()
        {
            // Try to get the configuration from a singleton or manager
            // For now, return null - this should be implemented based on your configuration system
            return null;
        }
    }
    
    /// <summary>
    /// Component to track performance metrics per entity
    /// </summary>
    public struct PerformanceMetricsComponent : IComponentData
    {
        public float LastUpdateTime;
        public int UpdateFrameInterval;
        public bool IsLowPriority;
    }
    
    /// <summary>
    /// System that applies frame-based update intervals to reduce CPU load
    /// </summary>
    [BurstCompile]
    public partial struct FrameIntervalSystem : ISystem
    {
        private int currentFrame;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            currentFrame = 0;
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            currentFrame++;
            
            // Update performance metrics for entities
            foreach (var (metrics, entity) in SystemAPI.Query<RefRW<PerformanceMetricsComponent>>().WithEntityAccess())
            {
                // Check if this entity should update this frame
                bool shouldUpdate = (currentFrame % metrics.ValueRO.UpdateFrameInterval) == 0;
                
                if (shouldUpdate)
                {
                    metrics.ValueRW.LastUpdateTime = (float)SystemAPI.Time.ElapsedTime;
                }
            }
        }
        
        /// <summary>
        /// Check if an entity should update based on its frame interval
        /// </summary>
        public static bool ShouldUpdateThisFrame(int currentFrame, int interval)
        {
            return (currentFrame % interval) == 0;
        }
    }
    
    /// <summary>
    /// System that manages memory allocation patterns to reduce GC pressure
    /// </summary>
    [BurstCompile]
    public partial struct MemoryOptimizationSystem : ISystem
    {
        private NativeArray<Entity> cachedEnemyEntities;
        private NativeList<float3> cachedPositions;
        private bool isInitialized;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            isInitialized = false;
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            if (!isInitialized)
            {
                Initialize(ref state);
                isInitialized = true;
            }
            
            // Reuse cached arrays instead of allocating new ones
            // This reduces GC pressure significantly
        }
        
        private void Initialize(ref SystemState state)
        {
            // Pre-allocate commonly used arrays
            int capacity = 200; // Should come from config
            
            cachedEnemyEntities = new NativeArray<Entity>(capacity, Allocator.Persistent);
            cachedPositions = new NativeList<float3>(capacity, Allocator.Persistent);
            
            Debug.Log($"[MemoryOptimization] Pre-allocated arrays with capacity: {capacity}");
        }
        
        [BurstCompile]
        public void OnDestroy(ref SystemState state)
        {
            if (cachedEnemyEntities.IsCreated)
                cachedEnemyEntities.Dispose();
            
            if (cachedPositions.IsCreated)
                cachedPositions.Dispose();
        }
        
        /// <summary>
        /// Get a reusable array for enemy entities
        /// </summary>
        public NativeArray<Entity> GetCachedEnemyArray()
        {
            return cachedEnemyEntities;
        }
        
        /// <summary>
        /// Get a reusable list for positions
        /// </summary>
        public NativeList<float3> GetCachedPositionList()
        {
            cachedPositions.Clear();
            return cachedPositions;
        }
    }
}
