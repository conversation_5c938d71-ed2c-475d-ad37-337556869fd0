using PlayerFAP.Mono.UI.Health;
using Unity.Entities;
using Unity.Transforms;
using UnityEngine;
using PlayerFAP.Components;
using PlayerFAP.Systems.AimDetection;
using Unity.Collections;
using Unity.Mathematics;

namespace PlayerFAP.Systems.UI
{
    [UpdateAfter(typeof(OptimizedDetectionSystem))]
    public partial class HealthBarSpawnSystem : SystemBase
    {
        private EntityQuery _detectedEnemiesQuery;
        private NativeList<Entity> _pooledUIEntities;

        protected override void OnCreate()
        {
            RequireForUpdate<DetectedTag>();
            RequireForUpdate<HealthComponent>();

            _detectedEnemiesQuery = GetEntityQuery(
                ComponentType.ReadOnly<DetectedTag>(),
                ComponentType.ReadOnly<HealthComponent>(),
                ComponentType.ReadOnly<LocalToWorld>(),
                ComponentType.ReadOnly<HealthBarUIOffset>(),
                ComponentType.Exclude<DeadTag>() // Exclude dead entities from health bar spawning
            );

            // Initialize entity pool
            _pooledUIEntities = new NativeList<Entity>(50, Allocator.Persistent);
        }

        protected override void OnDestroy()
        {
            // Clean up pooled entities
            if (_pooledUIEntities.IsCreated)
            {
                foreach (var entity in _pooledUIEntities)
                {
                    if (EntityManager.Exists(entity))
                    {
                        var companionLink = EntityManager.GetComponentData<CompanionLink>(entity);
                        if (companionLink.Companion != null && companionLink.Companion.Value != null)
                        {
                            HealthBarUIManager.Instance?.ReturnUIToPool(companionLink.Companion.Value);
                        }
                        EntityManager.DestroyEntity(entity);
                    }
                }
                _pooledUIEntities.Dispose();
            }
        }

        private Entity GetOrCreateUIEntity()
        {
            Entity uiEntity;
            if (_pooledUIEntities.Length > 0)
            {
                // Get entity from pool
                uiEntity = _pooledUIEntities[_pooledUIEntities.Length - 1];
                _pooledUIEntities.RemoveAt(_pooledUIEntities.Length - 1);
            }
            else
            {
                // Create new entity if pool is empty
                uiEntity = EntityManager.CreateEntity();
                EntityManager.AddComponent<CompanionLink>(uiEntity);
            }
            return uiEntity;
        }

        public void ReturnUIEntityToPool(Entity uiEntity)
        {
            if (EntityManager.Exists(uiEntity))
            {
                _pooledUIEntities.Add(uiEntity);
            }
        }

        protected override void OnUpdate()
        {
            if (HealthBarUIManager.Instance == null) return;

            var ecb = new EntityCommandBuffer(Unity.Collections.Allocator.Temp);

            // Only process entities that don't already have a health bar
            using (var entities = _detectedEnemiesQuery.ToEntityArray(Allocator.Temp))
            {
                for (int i = 0; i < entities.Length; i++)
                {
                    var entity = entities[i];

                    // Skip if already has health bar
                    if (SystemAPI.HasComponent<HealthBarUIReference>(entity))
                        continue;

                    // SAFETY CHECK: Skip dead entities (should already be excluded by query)
                    if (SystemAPI.HasComponent<DeadTag>(entity))
                    {
                        Debug.Log($"<color=red>[HealthBarSpawnSystem] Skipping dead entity {entity.Index} - should not reach here!</color>");
                        continue;
                    }

                    float poolPressure = HealthBarUIManager.Instance.PoolPressure;
                    //Debug.Log($"HealthBarSpawnSystem: Pool pressure before assignment: {poolPressure:P1}");

                    // Check if pool has available objects before proceeding
                    if (!HealthBarUIManager.Instance.HasAvailableObjects())
                    {
                        //Debug.LogWarning($"HealthBarSpawnSystem: No available UI objects in pool (pressure: {HealthBarUIManager.Instance.PoolPressure:P1}). Skipping health bar creation for remaining {sortedEntities.Length - i} entities.");
                        break;
                    }

                    var health = SystemAPI.GetComponent<HealthComponent>(entity);
                    var ltw = SystemAPI.GetComponent<LocalToWorld>(entity);
                    var offset = SystemAPI.GetComponent<HealthBarUIOffset>(entity);

                    var worldPosition = ltw.Position + math.mul(ltw.Rotation, offset.Offset);
                    
                    var healthBarGO = HealthBarUIManager.Instance.GetUIFromPool();
                    if (healthBarGO == null)
                    {
                        //Debug.LogWarning($"HealthBarSpawnSystem: GetUIFromPool returned null for entity {entity}. Pool may be exhausted.");
                        continue;
                    }

                    healthBarGO.transform.position = worldPosition;

                    var healthBarComponent = healthBarGO.GetComponent<HealthBarUI>();
                    healthBarComponent.SetHealth(health.CurrentHealth / health.MaxHealth);

                    // Get or create UI entity from pool
                    var uiEntity = GetOrCreateUIEntity();
                    EntityManager.SetComponentData(uiEntity, new CompanionLink { Companion = healthBarGO });
                    
                    ecb.AddComponent(entity, new HealthBarUIReference
                    {
                        UIEntity = uiEntity
                    });

                    //Debug.Log($"HealthBarSpawnSystem: Assigned health bar to entity {entity}. Pool pressure after assignment: {HealthBarUIManager.Instance.PoolPressure:P1}");
                }
            }

            ecb.Playback(EntityManager);
            ecb.Dispose();
        }
    }
    
    [UpdateAfter(typeof(OptimizedDetectionSystem))]
    [UpdateAfter(typeof(HealthBarSpawnSystem))]
    public partial class HealthBarUpdateSystem : SystemBase
    {
        private float _lastUpdateTime = 0f;
        private PlayerFAP.ScriptableObjects.HealthSystemConfiguration _config;

        protected override void OnCreate()
        {
            base.OnCreate();
            // Try to find the config asset in the scene/resources
            _config = UnityEngine.Resources.Load<PlayerFAP.ScriptableObjects.HealthSystemConfiguration>("HealthSystemConfiguration");
        }

        protected override void OnUpdate()
        {
            if (HealthBarUIManager.Instance == null) return;

            if (_config == null)
            {
                // Try to find again if not loaded
                _config = UnityEngine.Resources.Load<PlayerFAP.ScriptableObjects.HealthSystemConfiguration>("HealthSystemConfiguration");
            }
            float updateInterval = _config != null ? _config.healthBarUpdateInterval : 0.05f;

            var camera = Camera.main;
            if (camera == null) return;

            float currentTime = (float)SystemAPI.Time.ElapsedTime;
            if (currentTime - _lastUpdateTime < updateInterval)
                return;
            _lastUpdateTime = currentTime;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            Entities
                .WithNone<DeadTag>() // Exclude dead entities from health bar updates
                .ForEach((Entity entity, ref HealthComponent health, ref HealthBarUIOffset offset, ref LocalToWorld ltw, in HealthBarUIReference uiRef) =>
                {
                    if (camera == null) return;

                    var uiEntity = uiRef.UIEntity;
                    if (uiEntity == Entity.Null) return;

                    var companionLink = EntityManager.GetComponentData<CompanionLink>(uiEntity);
                    var uiObject = companionLink.Companion.Value;
                    if (uiObject == null) return;

                    // Calculate world position with offset in local space
                    var worldPosition = ltw.Position + math.mul(ltw.Rotation, offset.Offset);
                    
                    // Since we're using a world space canvas, directly set the world position
                    uiObject.transform.position = worldPosition;

                    // Make the UI always face the camera
                    uiObject.transform.rotation = camera.transform.rotation;

                    var healthBar = uiObject.GetComponent<HealthBarUI>();
                    if (healthBar != null)
                    {
                        healthBar.SetHealth(health.CurrentHealth / health.MaxHealth);
                    }
                }).WithoutBurst().Run();

            stopwatch.Stop();
            // Optional: log update time for diagnostics
            // Debug.Log($"HealthBarUpdateSystem: Update took {stopwatch.ElapsedMilliseconds} ms");
        }
    }
    
    [UpdateAfter(typeof(OptimizedDetectionSystem))]
    [UpdateAfter(typeof(HealthBarUpdateSystem))]
    public partial class HealthBarCleanupSystem : SystemBase
    {
        private HealthBarSpawnSystem _spawnSystem;

        protected override void OnCreate()
        {
            _spawnSystem = World.GetExistingSystemManaged<HealthBarSpawnSystem>();
            RequireForUpdate<HealthComponent>();
        }

        protected override void OnUpdate()
        {
            if (HealthBarUIManager.Instance == null) return;

            var ecb = new EntityCommandBuffer(Allocator.Temp);
            var spawnSystem = _spawnSystem;
            float dt = (float)SystemAPI.Time.DeltaTime;
            float gracePeriod = 0.5f;

            // 1. Increment timer for undetected entities (EXCLUDE dead entities - they're handled immediately)
            Entities
                .WithAll<HealthComponent, HealthBarUIReference>()
                .WithAbsent<DetectedTag, DeadTag>() // Exclude dead entities
                .ForEach((Entity entity, ref HealthBarCleanupTimerComponent timer) =>
                {
                    timer.UndetectedTime += dt;
                })
                .Schedule();

            // 2. Add timer to newly undetected entities (EXCLUDE dead entities)
            Entities
                .WithAll<HealthComponent, HealthBarUIReference>()
                .WithAbsent<DetectedTag, HealthBarCleanupTimerComponent, DeadTag>() // Exclude dead entities
                .ForEach((Entity entity) =>
                {
                    ecb.AddComponent(entity, new HealthBarCleanupTimerComponent { UndetectedTime = dt });
                })
                .WithoutBurst()
                .Run();

            // 3. Remove timer from re-detected entities
            Entities
                .WithAll<HealthComponent, HealthBarUIReference, DetectedTag>()
                .WithAll<HealthBarCleanupTimerComponent>()
                .WithAbsent<DeadTag>() // Exclude dead entities
                .ForEach((Entity entity) =>
                {
                    ecb.RemoveComponent<HealthBarCleanupTimerComponent>(entity);
                })
                .WithoutBurst()
                .Run();

            // 4. Cleanup after grace period (EXCLUDE dead entities - they're handled immediately)
            Entities
                .WithAll<HealthComponent, HealthBarUIReference>()
                .WithAbsent<DetectedTag, DeadTag>() // Exclude dead entities
                .ForEach((Entity entity, ref HealthBarUIReference uiRef, ref HealthBarCleanupTimerComponent timer) =>
                {
                    if (timer.UndetectedTime < gracePeriod) return;
                    if (uiRef.UIEntity == Entity.Null) return;

                    var companionLink = EntityManager.GetComponentData<CompanionLink>(uiRef.UIEntity);
                    var uiObject = companionLink.Companion.Value;
                    if (uiObject != null)
                    {
                        HealthBarUIManager.Instance.ReturnUIToPool(uiObject);
                    }

                    spawnSystem.ReturnUIEntityToPool(uiRef.UIEntity);
                    ecb.RemoveComponent<HealthBarUIReference>(entity);
                    ecb.RemoveComponent<HealthBarCleanupTimerComponent>(entity);
                    UnityEngine.Debug.Log($"[HealthBarCleanupSystem] Cleaned up health bar for entity {entity} after {timer.UndetectedTime:F2}s undetected.");
                })
                .WithoutBurst()
                .WithStructuralChanges()
                .Run();

            ecb.Playback(EntityManager);
            ecb.Dispose();
        }
    }
}